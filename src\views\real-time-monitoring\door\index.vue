<template>
  <div class="monitoring-door">
    <div class="content">
      <div class="door-list">
        <CardList ref="cardListRef" :list-config="listConfig" />
      </div>
      <!-- 实时日志 -->
      <div class="real-log">
        <h2 class="title">实时日志</h2>
        <RealLog ref="realLogRef" :height="doorListHeight" />
      </div>
    </div>
    <!-- 门信息编辑对话框 -->
    <DoorInfoEditDialog ref="dialogRef" @submit:success="cardListRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
import DoorInfoEditDialog from './components/DoorInfoEditDialog.vue'
import RealLog from './components/log/index.vue'
import { CardList, type ICardListConf } from '@/components/CardList'
import {
  controllerDoorInfoListAPI,
  // controllerDoorInfoDeleteAPI,
  controllerDoorInfoOpenAPI
} from '@/api/real-time-monitoring/door'
import { DICT_TYPE } from '@/utils/dict'
import { ElMessageBox } from 'element-plus'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({
  name: 'MonitoringDoor'
})

const message = useMessage()

/* 对话框组件实例 */
const dialogRef = ref<InstanceType<typeof DoorInfoEditDialog>>()
/** 卡片列表组件实例 */
const cardListRef = ref<InstanceType<typeof CardList>>()
/* 实时日志表格组件实例 */
const realLogRef = ref<InstanceType<typeof RealLog>>()
/** 门列表高度 */
const doorListHeight = ref(0)

const listConfig: ICardListConf = {
  fields: [
    {
      label: '名称',
      prop: 'doorName'
    },
    {
      label: '控制器编号',
      prop: 'controllerNumber'
    },
    {
      label: '工作模式',
      prop: 'workType',
      dictType: DICT_TYPE.DOOR_WORK_TYPE
    },
    {
      label: '门锁状态',
      prop: 'doorLockStatus',
      dictType: DICT_TYPE.DOOR_LOCK_STATUS
    },
    {
      label: '开门延时（ms）',
      prop: 'doorOpeningDelay'
    },
    {
      label: '闭门延时（ms）',
      prop: 'closedDoorDelay'
    }
  ],
  apiFn: controllerDoorInfoListAPI

  // actionsColumn: {
  //   actions: [
  //     {
  //       name: 'edit',
  //       type: 'primary',
  //       text: '编辑',
  //       onClick({ row }) {
  //         dialogRef.value?.open({
  //           title: '编辑门信息',
  //           isUpdate: true,
  //           row
  //         })
  //       }
  //     },
  //     {
  //       name: 'open',
  //       type: 'primary',
  //       text: '开门',
  //       onClick({ row }) {
  //         ElMessageBox.confirm('确定下发开门指令吗？', '提示', {
  //           confirmButtonText: '确定',
  //           cancelButtonText: '取消',
  //           type: 'warning',
  //           beforeClose: async (action, instance, done) => {
  //             if (action === 'confirm') {
  //               instance.confirmButtonLoading = true
  //               instance.confirmButtonText = 'Loading...'
  //               try {
  //                 await controllerDoorInfoOpenAPI(row.id, '21')
  //                 message.success('下发指令成功')
  //                 cardListRef.value?.refresh()
  //                 instance.confirmButtonLoading = false
  //                 done()
  //               } catch (error) {
  //                 console.log(error)
  //               }
  //             } else {
  //               done()
  //               message.info('取消')
  //             }
  //           }
  //         }).catch((error) => {
  //           console.log(error)
  //         })
  //       }
  //     }
  // {
  //   name: 'delete',
  //   type: 'danger',
  //   text: '删除',
  //   onClick({ row }) {
  //     ElMessageBox.confirm('是否删除该门信息', '提示', {
  //       confirmButtonText: '确定',
  //       cancelButtonText: '取消',
  //       type: 'warning'
  //     })
  //       .then(async () => {
  //         console.log('删除', row)
  //         try {
  //           await controllerDoorInfoDeleteAPI(row.id)
  //           ElMessage.success('删除成功')
  //           tableRef.value?.refresh()
  //         } catch (error) {
  //           console.log(error)
  //         }
  //       })
  //       .catch(() => {
  //         ElMessage.info('取消')
  //       })
  //   }
  // }
  // ]
  // }
}
</script>

<style lang="scss" scoped>
.monitoring-door {
  height: calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-footer-height) - 10px) !important;

  .content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;

    .door-list,
    .real-log {
      flex: 1 0 50%;
      overflow: hidden;
    }

    .real-log {
      .title {
        margin-bottom: 10px;
        font-size: 20px;
        color: #409eff;
      }
    }
  }
}
</style>

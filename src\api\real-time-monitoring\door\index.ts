import request from '@/config/axios'
import { ICDILAPIParams, IDoorInfoEditAPIData } from './types'
import type { IDoorInfo } from './types'

/**
 * @description 控制器们信息分页查询接口
 * @param data 请求参数
 * @param {Number} data.pageSize 请求参数
 * @param {Number} data.pageNo 请求参数
 * @param {String} data.doorName 请求参数
 * @returns
 */
export function controllerDoorInfoListAPI(data: ICDILAPIParams): Promise<PageResult<IDoorInfo[]>> {
  return request.post({
    url: '/door/controllerSettingsDoorInfo/page',
    data
  })
}

/**
 * @description 控制器们信息新增接口
 * @param data 请求参数
 * @param {Number} data.id id
 * @param {String} data.doorName 名称
 * @param {String} data.position 下标
 * @param {Number} data.workType 工作类型
 * @param {Number} data.doorOpeningDelay 开门延时
 * @param {Number} data.closedDoorDelay 关门延时
 * @param {Number} data.status 状态
 * @param {Number} data.doorLockStatus 门锁状态
 * @returns
 */
export function controllerDoorInfoAddAPI(data: IDoorInfoEditAPIData) {
  return request.post({
    url: '/door/controllerSettingsDoorInfo/add',
    data
  })
}

/**
 * @description 控制器们信息编辑接口
 * @param data 请求参数
 * @param {Number} data.id id
 * @param {String} data.doorName 名称
 * @param {String} data.position 下标
 * @param {Number} data.workType 工作类型
 * @param {Number} data.doorOpeningDelay 开门延时
 * @param {Number} data.closedDoorDelay 关门延时
 * @param {Number} data.status 状态
 * @param {Number} data.doorLockStatus 门锁状态
 * @returns
 */
export function controllerDoorInfoEditAPI(data: IDoorInfoEditAPIData) {
  return request.post({
    url: '/door/controllerSettingsDoorInfo/setDoorWorkType',
    data
  })
}

/**
 * @description 控制器门信息删除接口
 * @param id id
 * @returns
 */
export function controllerDoorInfoDeleteAPI(id: number) {
  return request.delete({
    url: '/door/controllerSettingsDoorInfo/delete',
    params: {
      id
    }
  })
}

/**
 * @description 控制器开门接口
 * @param id id
 * @returns
 */
export function controllerDoorInfoOpenAPI(id: number, state: string) {
  return request.get({
    url: '/door/controllerSettingsDoorInfo/openDoor',
    params: {
      id,
      state
    }
  })
}

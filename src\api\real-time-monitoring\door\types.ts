/* 控制门信息列表接口参数类型 */
export interface ICDILAPIParams extends ITableApiParams {
  doorName: number
}

/* 表单字段类型 */
export interface IDoorInfoEditAPIData {
  id?: number
  controllerNumber?: number
  doorName?: string
  position?: string
  workType?: number
  doorOpeningDelay?: number
  closedDoorDelay?: number
  status?: number
  doorLockStatus?: number
}

/** 门信息列表返回值类型 */
export interface IDoorInfo {
  createTime: string
  updateTime: string
  creator: null
  updater: null
  deleted: boolean
  id: number
  controllerNumber: number
  doorName: string
  position: number
  workType: number
  doorOpeningDelay: number
  closedDoorDelay: number
  status: number
  doorLockStatus: number
}

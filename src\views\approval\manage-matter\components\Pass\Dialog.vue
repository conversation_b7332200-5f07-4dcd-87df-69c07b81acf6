<template>
  <div class="pass-detail">
    <DetailDialog ref="detailDialog" title="详细信息" :authorize-type="authorizeType" :matter-id="matterId">
      <template #footer="{ approvalStatus }">
        <el-button type="primary" v-if="approvalStatus !== 5" @click="handleExecute">执行</el-button>
        <el-button type="default" @click="detailDialog?.close">关闭</el-button>
      </template>
    </DetailDialog>
  </div>
</template>

<script setup lang="ts">
import DetailDialog from '../DetailDialog.vue'

import { executePassMatterByIdAPI } from '@/api/approval'

import { ElLoading, ElMessage } from 'element-plus'

defineOptions({
  name: 'PassDetail'
})

const emit = defineEmits<{
  (e: 'submit:success'): void
}>()

/* 详情对话框组件实例 */
const detailDialog = ref<InstanceType<typeof DetailDialog>>()

/* 事项的id */
const matterId = ref()

/* 系统类型 */
const systemType = ref()

/* 事项的授权类型 */
const authorizeType = ref()

/* 加载动画 */
const loadingService = () => {
  return ElLoading.service({
    lock: true,
    text: '正在处理中...请勿操作，耐心等待~',
    background: 'rgba(0, 0, 0, 0.7)'
  })
}

/* 执行 */
const handleExecute = () => {
  let timer: NodeJS.Timeout | null = null
  let loading
  timer = setTimeout(() => {
    loading = loadingService()
    detailDialog.value!.setPercentage(100)
    detailDialog.value!.startPercentageAnimation()
  }, 200)

  executePassMatterByIdAPI(matterId.value, systemType.value)
    .then((data) => {
      ElMessage.success(data)
      emit('submit:success')
    })
    .catch((error) => {
      console.log(error)
      detailDialog.value!.setPercentage(0)
    })
    .finally(() => {
      clearTimeout(timer as NodeJS.Timeout)
      timer = null
      detailDialog.value!.closePercentageAnimation()
      loading && loading.close()
    })
}

const openDetailDialog = ({ id, authorizeType: type, status, systemType: system }) => {
  matterId.value = id
  authorizeType.value = type
  systemType.value = system
  detailDialog.value?.open({ status, systemType: system })
}

defineExpose({
  openDetailDialog
})
</script>

<template>
  <div class="car-auth-table">
    <BasicTable :table-config="tableConfig" />

    <!-- 查看权限状态对话框 -->
    <CarAuthStatusDialog ref="carAuthStatusDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import { authRecognitionAPI } from '@/api/synthesize-search'
import CarAuthStatusDialog from './CarAuthStatusDialog.vue'

defineOptions({
  name: 'CarAuthTable'
})

const carAuthStatusDialogRef = ref()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '设备名称',
      prop: 'equipmentName'
    },
    {
      label: '设备IP',
      prop: 'equipmentIp'
    },
    {
      label: '设备端口',
      prop: 'equipmentPort'
    },
    {
      label: '安装位置',
      prop: 'installationPosition'
    }
  ],
  beforeFetch() {
    return {
      deviceType: [5]
    }
  },
  apiFn: authRecognitionAPI,
  enableSearch: true,
  searchFormSchema: {
    formItems: [
      {
        field: 'equipmentNumber',
        component: 'Input',
        placeholder: '请输入设备编号',
        label: '设备编号'
      }
    ]
  },
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '查看权限状态',
        name: 'view',
        onClick: ({ row }) => {
          console.log(row)
          carAuthStatusDialogRef.value.open(row)
        }
      }
    ]
  },
  ortherHeight: 71 // el-tabs 的上下内边距、tab高度以及边框高度之和
}
</script>

<style lang="scss" scoped></style>

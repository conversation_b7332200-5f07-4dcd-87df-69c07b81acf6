<template>
  <div class="visitor-approval">
    <BasicTable ref="tableRef" :table-config="tableConfig">
      <template #car-number="{ row }">
        <span v-if="row.province || row.carNumber">{{ row.province }}·{{ row.carNumber }}</span>
        <span v-else>无</span>
      </template>
    </BasicTable>
    <EditRegisterInfo @register="register" @submit:success="handleSubmitSuccess" @reject="handleSubmitSuccess" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { queryApprovalRecordAPI, exportRegisterSheetAPI } from '@/api/visitor'
import EditRegisterInfo from './components/EditRegisterInfo.vue'
import { useModal } from '@/components/BaseModal/hooks/useModal'

defineOptions({
  name: 'VisitorApproval'
})

const [register, { openModal, closeModal }] = useModal()

/** 表格组件实例 */
const tableRef = ref<Nullable<IBasicTableExpose>>(null)

const tableConfig: ITableSchema = {
  beforeFetch() {
    return {
      visitorTypeList: [2]
    }
  },
  apiFn: queryApprovalRecordAPI,
  columns: [
    {
      label: '访客姓名',
      prop: 'visitorName'
    },
    {
      label: '性别',
      prop: 'sex',
      dictTag: true,
      dictType: DICT_TYPE.SYSTEM_USER_SEX
    },
    {
      label: '受访人姓名',
      prop: 'userName'
    },
    {
      label: '开始时间',
      prop: 'startTime',
      width: 180
    },
    {
      label: '结束时间',
      prop: 'endTime',
      width: 180
    },
    {
      label: '车牌号码',
      prop: 'carNumber',
      slot: 'car-number'
    },
    {
      label: '访问原因',
      prop: 'reason'
    }
  ],
  searchFormSchema: {
    formItems: [
      {
        label: '访客名称',
        field: 'userName',
        component: 'Input',
        placeholder: '请输入访客名称',
        componentProps: {
          clearable: true
        }
      },
      {
        label: '开始时间',
        field: 'startTime',
        component: 'DataTimePicker',
        placeholder: '请输入开始时间',
        componentProps: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true
        }
      },
      {
        label: '结束时间',
        field: 'endTime',
        component: 'DataTimePicker',
        placeholder: '请输入结束时间',
        componentProps: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true
        }
      }
    ]
  },
  toolbar: [
    {
      name: 'create',
      text: '填写登记表',
      icon: 'ep:plus',
      type: 'primary',
      onClick() {
        console.log('创建记录')
        openModal({
          data: {
            action: 'add'
          }
        })
      }
    },
    {
      name: 'export',
      text: '导出登记表',
      icon: 'ep:download',
      type: 'success',
      onClick() {
        exportRegisterSheetAPI()
          .then((res) => {
            const blob = new Blob([res], { type: 'application/vnd.ms-excel;charset=utf-8' })
            const url = URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.style.display = 'none'
            link.style.height = '0'
            link.href = url as string
            link.download = '登记表.xlsx'
            link.click()
            URL.revokeObjectURL(url)
          })
          .catch((error) => {
            console.log(error)
          })
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '详情',
        name: 'detail',
        onClick({ row }) {
          console.log('详情')
          openModal({
            data: {
              action: 'view',
              row
            }
          })
        }
      }
    ]
  }
}

/** 提交成功 */
const handleSubmitSuccess = () => {
  closeModal()
  tableRef.value?.refresh()
}
</script>

<style lang="scss" scoped></style>

<script lang="ts">
import { PropType, getCurrentInstance, withDirectives } from 'vue'
import type { ITableSchema, IButtonBtnSchema, ITableSearchForm } from '../types'
import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElTable,
  ElTableColumn,
  ElTooltip,
  TableInstance,
  vLoading
} from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { BaseForm } from '@/components/BaseForm'
import Pagination from '../../Pagination/index.vue'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { DictTag } from '@/components/DictTag'

export default defineComponent({
  name: 'BasicTable',
  components: {
    Icon,
    ElTable,
    ElButton,
    BaseForm,
    Pagination,
    ContentWrap
  },
  props: {
    tableConfig: {
      type: Object as PropType<ITableSchema>,
      required: true
    } // 表格配置
  },
  emits: ['search', 'reset'],
  setup(props, { emit, slots }) {
    const { t } = useI18n()
    const currentInstance = getCurrentInstance()

    const tableConfig = props.tableConfig

    const formRef = ref<InstanceType<typeof BaseForm>>()
    const paginationRef = ref<InstanceType<typeof Pagination>>()
    const loading = ref(false) // 列表的加载中
    const total = ref(0) // 列表的总页数
    const list = ref<any[]>([]) // 表的数据
    /* 已选择的表格数据 */
    const selectedTableData = ref<any[]>([])
    /* 展开/折叠 */
    const isExpandAll = ref(false)
    /** 是否展示搜索区域 */
    const expansionSearch = ref(false)
    /* 重新渲染表格 */
    const refreshTable = ref(true)
    /* 接口返回的数据 */
    const responseData = ref()
    /* 表格的高度 */
    const tableHeight = ref(0)
    /* 表格操作列按钮加载效果 */
    const actionBtnLoadingMap = ref<Record<string, boolean>>({})

    /* 查询参数 */
    const pageInfo = computed(() => {
      const pageInfo = {
        pageNo: 1,
        pageSize: 10
      }
      if (tableConfig.paginationProps) {
        pageInfo.pageNo = tableConfig.paginationProps.pageNo ?? 1
        pageInfo.pageSize = tableConfig.paginationProps.pageSize ?? 10
      }
      return pageInfo
    })

    /** 是否显示搜索的箭头 */
    const showArrow = computed(() => {
      const items = Array.from(document.querySelectorAll('.el-form-item'))
      const itemsWidth = items.reduce((result, item) => result + item.clientWidth, 0)
      const containerWidth = formRef.value?.$el.clientWidth
      console.log(containerWidth)
      console.log('🚀 ~ showArrow ~ itemsWidth ====> ', itemsWidth)
      return itemsWidth > containerWidth
    })

    /* 获取表格高度 */
    function getTableHeight() {
      /* 顶部工具栏高度 */
      const toolHeader = document.querySelector('#v-tool-header')
      const toolHeaderRect = toolHeader?.getBoundingClientRect()
      const toolHeaderHeight = toolHeaderRect?.height ?? 0

      /* 标签栏的高度 */
      const tagsView = document.querySelector('#v-tags-view')
      const tagsViewRect = tagsView?.getBoundingClientRect()
      const tagsViewHeight = tagsViewRect?.height ?? 0

      /* 内容区域的上内边距 */
      const sectionPaddingTop = 10
      const sectionBorderWidth = 2

      /* 表格搜索的高度 */
      const tableSearch = document.querySelector('.table-search')
      const tableSearchRect = tableSearch?.getBoundingClientRect()
      const tableSearchMargin = 15
      const tableSearchHeight = tableSearchRect?.height ? tableSearchMargin + tableSearchRect?.height : 0

      /* 表格所在卡片上下内边距 */
      const tableCardPadding = 20

      /* 底部的高度 */
      const footer = document.querySelector('.v-footer')
      const footerRect = footer?.getBoundingClientRect()
      const footerHeight = footerRect?.height ?? 0

      /* 表格tool工具栏高度 */
      const tableToolEl = document.querySelector('.table-tool')
      // console.dir(tableToolEl)
      const tableToolRect = tableToolEl?.getBoundingClientRect()
      const toolMargin = 10
      const tableToolHeight = tableToolRect?.height ? tableToolRect?.height + toolMargin : 0

      /* 分页器高度 */
      const paginationEl = document.querySelector('.pagination')
      let paginationHeight = paginationEl?.clientHeight || 54

      /* 其他高度 */
      const ortherHeight = tableConfig.ortherHeight
        ? typeof tableConfig.ortherHeight === 'function'
          ? tableConfig.ortherHeight()
          : tableConfig.ortherHeight
        : 0

      /* 设置表格的高度 */
      tableHeight.value =
        window.innerHeight -
        sectionPaddingTop -
        tableCardPadding -
        paginationHeight -
        ortherHeight -
        toolHeaderHeight -
        tagsViewHeight -
        tableSearchHeight -
        tableToolHeight -
        footerHeight -
        sectionBorderWidth
    }

    /* 表格头部搜索按钮点击事件 */
    const handleClick = (button: IButtonBtnSchema) => {
      if (button.name === 'refresh') {
        formRef.value?.resetFields()
        refresh()
      } else if (button.name === 'search') {
        console.log('搜索')
        getList()
      } else if (button.onClick && typeof button.onClick === 'function') {
        button.onClick(formRef.value?.formData, getList)
      }
    }

    /* 表格操作列按钮点击事件 */
    const handleActionBtnClick = async (action, scope) => {
      if (!action.enableLoading) return action.onClick?.(scope)
      try {
        actionBtnLoadingMap.value[action.name] = true
        await action.onClick?.(scope)
      } finally {
        actionBtnLoadingMap.value[action.name] = false
      }
    }

    /* 搜索 */
    const handleSearch = () => {
      emit('search', formRef.value?.formData)
      if (tableConfig.apiFn && typeof tableConfig.apiFn === 'function') {
        getList()
      }
    }

    /* 重置 */
    const handleReset = () => {
      pageInfo.value.pageNo = tableConfig.paginationProps?.pageNo ?? 1
      pageInfo.value.pageSize = tableConfig.paginationProps?.pageSize ?? 10
      formRef.value?.resetFields()
      emit('reset', formRef.value?.formData)
      if (tableConfig.apiFn && typeof tableConfig.apiFn === 'function') {
        getList()
      }
    }

    /* 获取列表数据 */
    const getList = async () => {
      loading.value = true
      try {
        /* 有传入api接口函数的情况： */
        if (tableConfig.apiFn) {
          /* 请求参数 */
          let params
          /* 调用请求前的钩子函数 */
          const addedParams = (tableConfig.beforeFetch && tableConfig.beforeFetch()) || {}
          /* 额外的请求参数 addedParams */
          params = { ...addedParams, ...formRef.value?.formData, ...pageInfo.value }
          /* 发送请求 */
          try {
            const data = await tableConfig.apiFn(params)
            responseData.value = data
            list.value = data.list ?? []
            total.value = data.total ?? 0
            tableConfig.afterFetch && tableConfig.afterFetch(list, tableConfig?.columns)
          } catch (error) {
            console.log(error)
          }
        } else if (tableConfig.data) {
          /* 不通过接口请求数据的情况 */
          const dataList = unref(tableConfig.data)
          if (Array.isArray(dataList)) {
            let start = (pageInfo.value.pageNo - 1) * pageInfo.value.pageSize
            let end = pageInfo.value.pageNo * pageInfo.value.pageSize
            const result: any[] = dataList.slice(start, end)
            list.value = result
            total.value = +dataList.length
          }
        }
        /* 获取表格数据 */
        list.value && setTableDataSource(list.value)
      } finally {
        loading.value = false
      }
    }

    /* 表格选择事件处理函数 */
    const handleSelectionChange = (newSelection: any[]) => {
      selectedTableData.value = newSelection
      tableConfig.selectionChange?.(newSelection)
    }

    /** 动态添加时不重复的表格数据 */
    let tableData = new Map<string, any>()

    const setTableDataSource = (data: any[]) => {
      if (!Array.isArray(data)) return
      data.forEach((item) => {
        if (!tableData.has(item.id)) {
          tableData.set(item.id, item)
        }
      })
    }

    const getResponseData = () => {
      return responseData.value
    }

    const getTableDataSource = () => {
      return tableData
    }

    /** 树形表格，展开/折叠操作 */
    const toggleExpandAll = () => {
      refreshTable.value = false
      isExpandAll.value = !isExpandAll.value
      nextTick(() => {
        refreshTable.value = true
      })
    }

    /* 刷新表格数据 */
    const refresh = () => {
      handleReset()
    }

    /* 表格数据 */
    const getTableList = () => {
      return new Promise((resolve) => {
        resolve(list.value)
      })
    }

    /* 获取表格数据的总数 */
    const getTotal = () => {
      return total.value
    }

    /** 搜索区域展开/收起 */
    const handleToggleSearchExpan = () => {
      expansionSearch.value = !expansionSearch.value
      if (!formRef.value) return
      if (expansionSearch.value) {
        formRef.value.$el.style.height = 'auto'
        formRef.value.$el.style.overflow = 'visible'
      } else {
        formRef.value.$el.style.height = '50px'
        formRef.value.$el.style.overflow = 'hidden'
      }
    }

    /* 更新表格指定行和列的值 */
    const updateTableDataByKey = (rowKey: string, columnKey: string, newValue: any) => {
      const row = tableData.get(rowKey)
      if (!row) return
      row[columnKey] = newValue
    }

    onMounted(async () => {
      getTableHeight()
      await getList()
      window.addEventListener('resize', getTableHeight)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', getTableHeight)
    })

    /** 向外暴露属性/方法 */
    function changeRef(tableInstance: TableInstance) {
      if (!currentInstance) return

      const expose = {
        getList,
        getTotal,
        refresh,
        getTableList,
        toggleExpandAll,
        getResponseData,
        getTableDataSource,
        updateTableDataByKey
      }

      /** tableInstance解构（遍历属性），Vue会报警告，所以采取下面方式： */
      currentInstance.exposeProxy = currentInstance.exposed = tableInstance

      Object.keys(expose).forEach((key) => {
        if (currentInstance.exposed) {
          currentInstance.exposed[key] = expose[key]
        }
      })

      currentInstance.exposeProxy = currentInstance.exposed
    }

    /** 视图层 */

    /** 表格头部 */

    function tableHeaderRender() {
      /** 表格搜索区域搜索&重置按钮 */
      const showText = tableConfig.searchFormSchema?.showText ?? true
      const searchBtn = !tableConfig.searchFormSchema?.hiddenSearch
        ? showText
          ? h(ElButton, { type: 'primary', icon: Search, onClick: handleSearch }, '搜索')
          : h(ElButton, { type: 'primary', icon: Search, onClick: handleSearch })
        : null
      const resetBtn = !tableConfig.searchFormSchema?.hiddenReset
        ? showText
          ? h(ElButton, { icon: Refresh, onClick: handleReset }, '重置')
          : h(ElButton, { icon: Refresh, onClick: handleReset })
        : null

      /** card-body 卡片的样式 */
      const bodyStyle = tableConfig.searchCardStyle?.body
      const noBorder = tableConfig.searchCardStyle?.showBorder === false ? 'not-border' : null

      const formSchema = Object.assign({}, tableConfig.searchFormSchema, {
        inline: true,
        colProps: {
          span: null
        }
      }) as ITableSearchForm

      const enableSearch = tableConfig.enableSearch ?? true

      console.log(showArrow.value)

      /** 展开搜索区域箭头 */
      const arrowRender = () => {
        /** 是否显示 */
        /** 显示上还是下 */
        const icon = expansionSearch.value ? 'ep:arrow-up' : 'ep:arrow-down'
        const toolTipContent = expansionSearch.value ? '收起' : '展开'
        return showArrow.value
          ? h(ElTooltip, { content: toolTipContent }, () =>
              h(Icon, { icon, class: 'expan-arrow', onClick: handleToggleSearchExpan })
            )
          : null
      }

      return enableSearch
        ? h(
            ContentWrap,
            {
              class: ['table-search', noBorder],
              bodyStyle,
              bodyClass: 'table-search__body'
            },
            {
              default: () => [
                h(
                  BaseForm,
                  { ref: formRef, formSchema },
                  { col: () => h('div', { class: 'table-search__btn-group' }, [searchBtn, resetBtn, arrowRender()]) }
                )
              ]
            }
          )
        : null
    }

    /** 表格 */

    /** 表格列表工具栏 */
    function tableToolRender() {
      return h('div', { class: 'table-tool' }, [
        h(
          'div',
          { class: 'tool-btns' },
          tableConfig.toolbar?.map((button) =>
            h(
              ElButton,
              {
                key: button.name,
                onClick: () => handleClick(button),
                plain: button.plain ?? true,
                type: button.type,
                class: button.name
              },
              () => [
                h(Icon, { icon: button.icon, iconColor: button.iconColor }),
                button.text && h('span', null, button.text)
              ]
            )
          )
        ),
        h('div', { class: 'toll-utils' })
      ])
    }

    /** 列表 */
    function tableListRender() {
      const SelectionColumn = tableConfig.allowSelection
        ? h(ElTableColumn, {
            type: 'selection',
            selectable: tableConfig.checkboxProps?.selectable,
            reserveSelection: tableConfig.checkboxProps?.reserveSelection
          })
        : null

      /** 内容列 */
      function ColumnsRender() {
        return tableConfig.columns.map((column) =>
          h(
            ElTableColumn,
            {
              key: column.prop,
              align: tableConfig.align ?? column.align ?? 'center',
              prop: column.prop,
              width: column.width,
              fixed: column.fixed,
              showOverflowTooltip: column.toolTip ?? true
            },
            {
              header: () => {
                const label = typeof column.label === 'function' ? column.label(list.value, column) : column.label
                return slots.header ? slots.header() : label ? label : null
              },
              default: (scope) => {
                if (column.dictTag && ![undefined, null].includes(scope.row[column.prop])) {
                  return h(DictTag, { type: column.dictType as string, value: scope.row[column.prop] })
                } else if (column.slot) {
                  return slots[column.slot]?.(scope)
                } else if (column.render) {
                  return column.render(scope)
                } else {
                  return h('span', null, [
                    column.formatter && typeof column.formatter === 'function'
                      ? column.formatter(scope)
                      : scope.row[column.prop]
                  ])
                }
              }
            }
          )
        )
      }

      /** 操作列按钮 */
      function operationsBtns(scope) {
        const limit = tableConfig.actionsColumn?.limit ?? tableConfig.actionsColumn?.actions.length
        return tableConfig.actionsColumn?.actions.slice(0, limit).map((action) => {
          const type = action.type ? (typeof action.type === 'function' ? action.type(scope) : action.type) : 'primary'
          const disabled = action.disabled
            ? typeof action.disabled === 'function'
              ? action.disabled(scope)
              : action.disabled
            : false

          const hidden = action.hidden
            ? typeof action.hidden === 'function'
              ? action.hidden(scope)
              : action.hidden
            : false

          const icon = action.icon ? h(Icon, { icon: action.icon }) : null
          const text = action.text
            ? typeof action.text === 'function'
              ? action.text(scope)
                ? h('span', {}, action.text(scope))
                : null
              : h('span', {}, action.text)
            : null
          return !hidden
            ? h(
                ElButton,
                {
                  type,
                  link: action.link ?? true,
                  disabled,
                  loading: actionBtnLoadingMap[action.name + scope.$index],
                  vHasPermi: action.permission,
                  onClick: () => handleActionBtnClick(action, scope)
                },
                () => [icon, text]
              )
            : null
        })
      }

      /** 下拉菜单 */
      function operationDropdown(scope) {
        const showDropdown =
          tableConfig.actionsColumn?.actions.length &&
          tableConfig.actionsColumn?.limit &&
          tableConfig.actionsColumn?.actions.length > tableConfig.actionsColumn?.limit

        const items = tableConfig.actionsColumn?.actions.slice(tableConfig.actionsColumn.limit)

        return showDropdown
          ? h(
              ElDropdown,
              { class: 'more', size: 'small' },
              {
                default: () =>
                  h(ElButton, { type: 'primary', link: true }, () => [
                    h('span', null, t('action.more')),
                    h(Icon, { icon: 'ep:d-arrow-right', size: 14 })
                  ]),
                dropdown: () =>
                  items?.map((item) =>
                    h(ElDropdownItem, { key: item.name, onClick: () => item.onClick?.(scope) }, () =>
                      typeof item.text === 'function' ? item.text(scope) : item.text
                    )
                  )
              }
            )
          : null
      }

      /** 操作列 */
      const operationsColumn = tableConfig.showActions
        ? h(
            ElTableColumn,
            {
              label: '操作',
              width: tableConfig.actionsColumn?.width ?? 180,
              className: 'table-action'
            },
            {
              default: (scope) => [operationsBtns(scope), operationDropdown(scope)]
            }
          )
        : null

      return withDirectives(
        h(
          ElTable,
          {
            'v-loading': loading.value,
            ref: changeRef,
            data: list.value,
            rowKey: tableConfig.rowKey ?? 'id',
            class: 'talbe',
            height: tableHeight.value,
            maxHeight: tableHeight.value,
            defaultExpandAll: isExpandAll.value,
            headerCellStyle: { backgroundColor: 'var(--app-content-bg-color)' },
            onSelectionChange: handleSelectionChange
          },
          () => [SelectionColumn, ColumnsRender(), operationsColumn]
        ),
        [[vLoading, loading.value]]
      )
    }

    /** 分页器 */
    function paginationRender() {
      return h(
        'div',
        { class: 'pagination' },
        h(Pagination, {
          class: 'pagination',
          ref: paginationRef,
          limit: pageInfo.value.pageSize,
          page: pageInfo.value.pageNo,
          'onUpdate:limit': (value) => {
            pageInfo.value.pageSize = value
          },
          'onUpdate:page': (value) => {
            pageInfo.value.pageNo = value
          },
          total: total.value,
          pagerCount: tableConfig.paginationProps?.pagerCount,
          layout: tableConfig.paginationProps?.layout,
          size: 'small',
          onPagination: getList
        })
      )
    }

    /** 表格 */

    const showToolbar = tableConfig.toolbar && tableConfig.toolbar.length !== 0

    function tableRender() {
      const bodyStyle = tableConfig.tableCardStyle?.body || { padding: '10px' }
      const noBorder = tableConfig.tableCardStyle?.showBorder === false ? 'not-border' : null
      return h(ContentWrap, { class: ['table-area', noBorder], bodyStyle }, () => [
        showToolbar && tableToolRender(),
        tableListRender(),
        paginationRender()
      ])
    }

    return function render() {
      return h('div', { class: 'basic-table' }, [tableHeaderRender(), tableRender()])
    }
  }
})
</script>

<style lang="scss" scoped>
.not-border {
  border: 0;
}

.basic-table {
  width: 100%;

  .table {
    &-search {
      &:deep(.table-search__body) {
        position: relative;
      }

      .base-form {
        width: calc(100% - 200px);
        height: 50px;
        overflow: hidden;

        &:deep(.el-form) {
          .el-row {
            position: static;
          }
        }
      }

      &:deep(.table-search__body) {
        padding: 18px 10px 0 !important;
      }

      &__btn-group {
        position: absolute;
        right: 10px;
        bottom: 18px;
        display: flex;
        align-items: center;

        .expan-arrow {
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }

    &-area {
      width: 100%;
      margin-bottom: 0;

      &:deep(.table-action) {
        .cell {
          display: flex;
          align-items: center;
        }

        .more {
          margin-left: 12px;
        }
      }
    }

    &-tool {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .el-dropdown-link {
      display: flex;
      color: var(--el-color-primary);
      cursor: pointer;
      align-items: center;
    }
  }

  .pagination {
    position: relative;
    padding-right: 15px;
    // height: 54px;
    overflow: hidden;

    &:deep(.el-input),
    &:deep(.el-select) {
      min-width: auto;
    }
  }
}
</style>
